//
//  LocationManagerTestViewController.m
//  测试控制器：用于验证LocationManager优化效果
//

#import "LocationManagerTestViewController.h"
#import "LocationManager.h"

@interface LocationManagerTestViewController ()
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, assign) NSInteger testCounter;
@end

@implementation LocationManagerTestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    self.title = @"LocationManager Test";
    self.testCounter = 0;
    
    [self setupUI];
    
    NSLog(@"🧪 [测试页面] LocationManager测试页面已加载");
    NSLog(@"🧪 [测试页面] 请点击按钮测试不同场景的定位功能");
    NSLog(@"🧪 [测试页面] 观察控制台输出验证优化效果");
}

- (void)setupUI {
    // 创建滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];
    
    // 创建垂直堆栈视图
    self.stackView = [[UIStackView alloc] init];
    self.stackView.axis = UILayoutConstraintAxisVertical;
    self.stackView.spacing = 20;
    self.stackView.alignment = UIStackViewAlignmentFill;
    self.stackView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.stackView];
    
    // 添加测试按钮
    [self addTestButton:@"🎯 单次埋点测试" action:@selector(testSingleTracking)];
    [self addTestButton:@"🔄 连续埋点测试 (3次)" action:@selector(testMultipleTracking)];
    [self addTestButton:@"⚡ 快速连续埋点 (5次)" action:@selector(testRapidTracking)];
    [self addTestButton:@"⏰ 超时测试 (飞行模式)" action:@selector(testTimeoutScenario)];
    [self addTestButton:@"💾 查看缓存坐标" action:@selector(testCachedCoordinate)];
    [self addTestButton:@"🧹 清空控制台" action:@selector(clearConsole)];
    
    // 添加说明文本
    [self addInfoLabel];
    
    // 设置约束
    [self setupConstraints];
}

- (void)addTestButton:(NSString *)title action:(SEL)action {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
    [button setTitle:title forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont systemFontOfSize:16];
    button.backgroundColor = [UIColor systemBlueColor];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.layer.cornerRadius = 8;
    button.contentEdgeInsets = UIEdgeInsetsMake(12, 16, 12, 16);
    [button addTarget:self action:action forControlEvents:UIControlEventTouchUpInside];
    
    [self.stackView addArrangedSubview:button];
    
    // 设置按钮高度
    [button.heightAnchor constraintEqualToConstant:44].active = YES;
}

- (void)addInfoLabel {
    UILabel *infoLabel = [[UILabel alloc] init];
    infoLabel.text = @"📱 测试说明：\n\n1. 点击按钮测试不同场景\n2. 观察Xcode控制台输出\n3. 验证每次埋点都能获得坐标\n4. 检查8秒超时兜底机制\n5. 确认多个请求不会互相干扰\n\n🔍 关键验证点：\n• 每次埋点创建新的CLLocationManager\n• 8秒超时后使用缓存坐标兜底\n• 多个并发请求互不影响\n• 坐标缓存机制正常工作";
    infoLabel.numberOfLines = 0;
    infoLabel.font = [UIFont systemFontOfSize:14];
    infoLabel.textColor = [UIColor darkGrayColor];
    infoLabel.backgroundColor = [UIColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0];
    infoLabel.layer.cornerRadius = 8;
    infoLabel.layer.masksToBounds = YES;
    infoLabel.textAlignment = NSTextAlignmentLeft;
    
    // 添加内边距
    UIView *containerView = [[UIView alloc] init];
    containerView.backgroundColor = [UIColor clearColor];
    [containerView addSubview:infoLabel];
    
    infoLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [infoLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:16],
        [infoLabel.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor constant:16],
        [infoLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor constant:-16],
        [infoLabel.bottomAnchor constraintEqualToAnchor:containerView.bottomAnchor constant:-16]
    ]];
    
    [self.stackView addArrangedSubview:containerView];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // ScrollView约束
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],
        
        // StackView约束
        [self.stackView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor constant:20],
        [self.stackView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor constant:20],
        [self.stackView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor constant:-20],
        [self.stackView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor constant:-20],
        [self.stackView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor constant:-40]
    ]];
}

#pragma mark - Test Methods

- (void)testSingleTracking {
    self.testCounter++;
    NSLog(@"🧪 [测试] ========== 开始单次埋点测试 #%ld ==========", (long)self.testCounter);
    
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSLog(@"🧪 [测试] 单次埋点测试 #%ld 完成", (long)self.testCounter);
        NSLog(@"🧪 [测试] 获得坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
        if (error) {
            NSLog(@"🧪 [测试] 使用了兜底机制: %@", error.localizedDescription);
        } else {
            NSLog(@"🧪 [测试] 获得了实时定位");
        }
        NSLog(@"🧪 [测试] ========== 单次埋点测试完成 ==========\n");
    }];
}

- (void)testMultipleTracking {
    NSLog(@"🧪 [测试] ========== 开始连续埋点测试 (3次) ==========");
    
    for (int i = 1; i <= 3; i++) {
        self.testCounter++;
        NSInteger currentTest = self.testCounter;
        
        NSLog(@"🧪 [测试] 启动第 %d 个埋点请求 #%ld", i, (long)currentTest);
        
        [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
            NSLog(@"🧪 [测试] 连续埋点 #%ld (第%d个) 完成", (long)currentTest, i);
            NSLog(@"🧪 [测试] 坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
            if (error) {
                NSLog(@"🧪 [测试] 使用兜底: %@", error.localizedDescription);
            }
        }];
        
        // 间隔1秒发送下一个请求
        if (i < 3) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                // 下一个请求将在这里执行
            });
        }
    }
}

- (void)testRapidTracking {
    NSLog(@"🧪 [测试] ========== 开始快速连续埋点测试 (5次) ==========");
    NSLog(@"🧪 [测试] 验证多个并发请求是否会互相干扰");
    
    for (int i = 1; i <= 5; i++) {
        self.testCounter++;
        NSInteger currentTest = self.testCounter;
        
        NSLog(@"🧪 [测试] 快速启动第 %d 个埋点请求 #%ld", i, (long)currentTest);
        
        [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
            NSLog(@"🧪 [测试] 快速埋点 #%ld (第%d个) 完成", (long)currentTest, i);
            NSLog(@"🧪 [测试] 坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
        }];
    }
}

- (void)testTimeoutScenario {
    NSLog(@"🧪 [测试] ========== 超时场景测试 ==========");
    NSLog(@"🧪 [测试] 提示: 如需测试真实超时，请开启飞行模式后点击此按钮");
    NSLog(@"🧪 [测试] 正常情况下会在8秒内完成定位或触发兜底");
    
    self.testCounter++;
    NSInteger currentTest = self.testCounter;
    NSDate *startTime = [NSDate date];
    
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:startTime];
        NSLog(@"🧪 [测试] 超时测试 #%ld 完成，耗时: %.1f秒", (long)currentTest, elapsed);
        NSLog(@"🧪 [测试] 坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
        if (error) {
            NSLog(@"🧪 [测试] 触发了兜底机制: %@", error.localizedDescription);
        }
        NSLog(@"🧪 [测试] ========== 超时测试完成 ==========\n");
    }];
}

- (void)testCachedCoordinate {
    CLLocationCoordinate2D cached = [[LocationManager sharedManager] getCachedCoordinate];
    NSLog(@"🧪 [测试] ========== 缓存坐标查看 ==========");
    NSLog(@"🧪 [测试] 当前缓存坐标: %.6f, %.6f", cached.latitude, cached.longitude);
    NSLog(@"🧪 [测试] 坐标有效性: %@", CLLocationCoordinate2DIsValid(cached) ? @"有效" : @"无效");
    NSLog(@"🧪 [测试] ========== 缓存坐标查看完成 ==========\n");
}

- (void)clearConsole {
    NSLog(@"\n\n\n\n\n\n\n\n\n\n");
    NSLog(@"🧹 [测试] ========== 控制台已清空 ==========");
    NSLog(@"🧹 [测试] 可以开始新的测试了");
    NSLog(@"🧹 [测试] ========================================\n");
}

@end
