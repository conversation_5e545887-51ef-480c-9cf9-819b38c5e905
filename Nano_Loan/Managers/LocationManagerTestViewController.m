//
//  LocationManagerTestViewController.m
//  测试控制器：用于验证LocationManager优化效果
//

#import "LocationManagerTestViewController.h"
#import "LocationManager.h"

@interface LocationManagerTestViewController ()
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, assign) NSInteger testCounter;
@end

@implementation LocationManagerTestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    self.title = @"LocationManager Test";
    self.testCounter = 0;
    
    [self setupUI];
    
    NSLog(@"🧪 [测试页面] LocationManager测试页面已加载");
    NSLog(@"🧪 [测试页面] 请点击按钮测试不同场景的定位功能");
    NSLog(@"🧪 [测试页面] 观察控制台输出验证优化效果");
}

- (void)setupUI {
    // 创建滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];
    
    // 创建垂直堆栈视图
    self.stackView = [[UIStackView alloc] init];
    self.stackView.axis = UILayoutConstraintAxisVertical;
    self.stackView.spacing = 20;
    self.stackView.alignment = UIStackViewAlignmentFill;
    self.stackView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.stackView];
    
    // 添加测试按钮
    [self addTestButton:@"🎯 高精度单次测试" action:@selector(testSingleTracking)];
    [self addTestButton:@"🔄 连续精度测试 (3次)" action:@selector(testMultipleTracking)];
    [self addTestButton:@"⚡ 快速连续测试 (5次)" action:@selector(testRapidTracking)];
    [self addTestButton:@"📏 精度对比测试" action:@selector(testAccuracyComparison)];
    [self addTestButton:@"⏰ 超时测试 (飞行模式)" action:@selector(testTimeoutScenario)];
    [self addTestButton:@"💾 查看缓存坐标" action:@selector(testCachedCoordinate)];
    [self addTestButton:@"🧹 清空控制台" action:@selector(clearConsole)];
    
    // 添加说明文本
    [self addInfoLabel];
    
    // 设置约束
    [self setupConstraints];
}

- (void)addTestButton:(NSString *)title action:(SEL)action {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
    [button setTitle:title forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont systemFontOfSize:16];
    button.backgroundColor = [UIColor systemBlueColor];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.layer.cornerRadius = 8;
    button.contentEdgeInsets = UIEdgeInsetsMake(12, 16, 12, 16);
    [button addTarget:self action:action forControlEvents:UIControlEventTouchUpInside];
    
    [self.stackView addArrangedSubview:button];
    
    // 设置按钮高度
    [button.heightAnchor constraintEqualToConstant:44].active = YES;
}

- (void)addInfoLabel {
    UILabel *infoLabel = [[UILabel alloc] init];
    infoLabel.text = @"📱 高精度定位测试：\n\n🎯 新特性验证：\n• 导航级最高精度配置\n• 智能精度优化(目标1-3米)\n• 20秒超时 + 最佳位置选择\n• iOS 14+临时完整精度权限\n• 0.1米距离过滤\n\n🔍 测试重点：\n• 观察8位小数精度坐标输出\n• 验证精度从粗到细的优化过程\n• 检查5G环境下是否达到米级精度\n• 确认连续埋点坐标差异性\n\n📊 精度目标：\n• 最佳: ≤1米 (厘米级)\n• 良好: ≤3米\n• 可接受: ≤10米";
    infoLabel.numberOfLines = 0;
    infoLabel.font = [UIFont systemFontOfSize:14];
    infoLabel.textColor = [UIColor darkGrayColor];
    infoLabel.backgroundColor = [UIColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0];
    infoLabel.layer.cornerRadius = 8;
    infoLabel.layer.masksToBounds = YES;
    infoLabel.textAlignment = NSTextAlignmentLeft;
    
    // 添加内边距
    UIView *containerView = [[UIView alloc] init];
    containerView.backgroundColor = [UIColor clearColor];
    [containerView addSubview:infoLabel];
    
    infoLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [infoLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:16],
        [infoLabel.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor constant:16],
        [infoLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor constant:-16],
        [infoLabel.bottomAnchor constraintEqualToAnchor:containerView.bottomAnchor constant:-16]
    ]];
    
    [self.stackView addArrangedSubview:containerView];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // ScrollView约束
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],
        
        // StackView约束
        [self.stackView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor constant:20],
        [self.stackView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor constant:20],
        [self.stackView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor constant:-20],
        [self.stackView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor constant:-20],
        [self.stackView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor constant:-40]
    ]];
}

#pragma mark - Test Methods

- (void)testSingleTracking {
    self.testCounter++;
    NSLog(@"🧪 [高精度测试] ========== 开始单次高精度埋点测试 #%ld ==========", (long)self.testCounter);
    NSLog(@"🧪 [高精度测试] 期望精度: 1-3米 (厘米级到米级)");

    NSDate *startTime = [NSDate date];
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:startTime];

        NSLog(@"🧪 [高精度测试] 单次测试 #%ld 完成，耗时: %.1f秒", (long)self.testCounter, elapsed);
        NSLog(@"🧪 [高精度测试] 高精度坐标: %.8f, %.8f", coordinate.latitude, coordinate.longitude);

        if (error) {
            NSLog(@"🧪 [高精度测试] 使用了兜底机制: %@", error.localizedDescription);
        } else {
            NSLog(@"🧪 [高精度测试] ✅ 获得了实时高精度定位");
        }

        // 计算与缓存坐标的距离差异
        CLLocationCoordinate2D cached = [[LocationManager sharedManager] getCachedCoordinate];
        if (CLLocationCoordinate2DIsValid(cached)) {
            CLLocation *loc1 = [[CLLocation alloc] initWithLatitude:coordinate.latitude longitude:coordinate.longitude];
            CLLocation *loc2 = [[CLLocation alloc] initWithLatitude:cached.latitude longitude:cached.longitude];
            CLLocationDistance distance = [loc1 distanceFromLocation:loc2];
            NSLog(@"🧪 [高精度测试] 与缓存坐标距离差: %.2f米", distance);
        }

        NSLog(@"🧪 [高精度测试] ========== 单次高精度测试完成 ==========\n");
    }];
}

- (void)testMultipleTracking {
    NSLog(@"🧪 [测试] ========== 开始连续埋点测试 (3次) ==========");
    
    for (int i = 1; i <= 3; i++) {
        self.testCounter++;
        NSInteger currentTest = self.testCounter;
        
        NSLog(@"🧪 [测试] 启动第 %d 个埋点请求 #%ld", i, (long)currentTest);
        
        [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
            NSLog(@"🧪 [测试] 连续埋点 #%ld (第%d个) 完成", (long)currentTest, i);
            NSLog(@"🧪 [测试] 坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
            if (error) {
                NSLog(@"🧪 [测试] 使用兜底: %@", error.localizedDescription);
            }
        }];
        
        // 间隔1秒发送下一个请求
        if (i < 3) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                // 下一个请求将在这里执行
            });
        }
    }
}

- (void)testRapidTracking {
    NSLog(@"🧪 [测试] ========== 开始快速连续埋点测试 (5次) ==========");
    NSLog(@"🧪 [测试] 验证多个并发请求是否会互相干扰");
    
    for (int i = 1; i <= 5; i++) {
        self.testCounter++;
        NSInteger currentTest = self.testCounter;
        
        NSLog(@"🧪 [测试] 快速启动第 %d 个埋点请求 #%ld", i, (long)currentTest);
        
        [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
            NSLog(@"🧪 [测试] 快速埋点 #%ld (第%d个) 完成", (long)currentTest, i);
            NSLog(@"🧪 [测试] 坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
        }];
    }
}

- (void)testAccuracyComparison {
    NSLog(@"🧪 [精度对比] ========== 开始精度对比测试 ==========");
    NSLog(@"🧪 [精度对比] 连续进行5次定位，观察坐标变化和精度提升");

    __block NSMutableArray<NSDictionary *> *results = [NSMutableArray array];
    __block NSInteger completedCount = 0;

    for (int i = 1; i <= 5; i++) {
        self.testCounter++;
        NSInteger currentTest = self.testCounter;

        NSLog(@"🧪 [精度对比] 启动第 %d 次精度测试 #%ld", i, (long)currentTest);

        NSDate *startTime = [NSDate date];
        [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
            NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:startTime];

            // 记录结果
            NSDictionary *result = @{
                @"index": @(i),
                @"testId": @(currentTest),
                @"coordinate": [NSValue valueWithBytes:&coordinate objCType:@encode(CLLocationCoordinate2D)],
                @"elapsed": @(elapsed),
                @"error": error ?: [NSNull null]
            };
            [results addObject:result];
            completedCount++;

            NSLog(@"🧪 [精度对比] 第%d次完成: %.8f, %.8f (耗时: %.1fs)", i, coordinate.latitude, coordinate.longitude, elapsed);

            // 所有测试完成后进行对比分析
            if (completedCount == 5) {
                [self analyzeAccuracyResults:results];
            }
        }];

        // 间隔2秒进行下一次测试
        if (i < 5) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                // 下一个测试将自动开始
            });
        }
    }
}

- (void)analyzeAccuracyResults:(NSArray<NSDictionary *> *)results {
    NSLog(@"🧪 [精度分析] ========== 精度对比分析 ==========");

    NSMutableArray<CLLocation *> *locations = [NSMutableArray array];

    for (NSDictionary *result in results) {
        CLLocationCoordinate2D coord;
        [result[@"coordinate"] getValue:&coord];
        CLLocation *location = [[CLLocation alloc] initWithLatitude:coord.latitude longitude:coord.longitude];
        [locations addObject:location];

        NSLog(@"🧪 [精度分析] 测试%@: %.8f, %.8f (耗时: %.1fs)",
              result[@"index"], coord.latitude, coord.longitude, [result[@"elapsed"] doubleValue]);
    }

    // 计算坐标变化范围
    if (locations.count >= 2) {
        CLLocationDistance maxDistance = 0;
        CLLocationDistance minDistance = MAXFLOAT;
        CLLocationDistance totalDistance = 0;
        NSInteger distanceCount = 0;

        for (int i = 0; i < locations.count; i++) {
            for (int j = i + 1; j < locations.count; j++) {
                CLLocationDistance distance = [locations[i] distanceFromLocation:locations[j]];
                maxDistance = MAX(maxDistance, distance);
                minDistance = MIN(minDistance, distance);
                totalDistance += distance;
                distanceCount++;
            }
        }

        CLLocationDistance avgDistance = totalDistance / distanceCount;

        NSLog(@"🧪 [精度分析] 坐标变化统计:");
        NSLog(@"🧪 [精度分析] • 最大距离差: %.2f米", maxDistance);
        NSLog(@"🧪 [精度分析] • 最小距离差: %.2f米", minDistance);
        NSLog(@"🧪 [精度分析] • 平均距离差: %.2f米", avgDistance);

        // 精度评估
        if (maxDistance <= 1.0) {
            NSLog(@"🏆 [精度分析] 精度评级: 优秀 (厘米级精度)");
        } else if (maxDistance <= 3.0) {
            NSLog(@"✅ [精度分析] 精度评级: 良好 (米级精度)");
        } else if (maxDistance <= 10.0) {
            NSLog(@"⚡ [精度分析] 精度评级: 可接受 (10米内精度)");
        } else {
            NSLog(@"⚠️ [精度分析] 精度评级: 需要优化 (超过10米差异)");
        }
    }

    NSLog(@"🧪 [精度分析] ========== 精度对比分析完成 ==========\n");
}

- (void)testTimeoutScenario {
    NSLog(@"🧪 [测试] ========== 超时场景测试 ==========");
    NSLog(@"🧪 [测试] 提示: 如需测试真实超时，请开启飞行模式后点击此按钮");
    NSLog(@"🧪 [测试] 正常情况下会在8秒内完成定位或触发兜底");
    
    self.testCounter++;
    NSInteger currentTest = self.testCounter;
    NSDate *startTime = [NSDate date];
    
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:startTime];
        NSLog(@"🧪 [测试] 超时测试 #%ld 完成，耗时: %.1f秒", (long)currentTest, elapsed);
        NSLog(@"🧪 [测试] 坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
        if (error) {
            NSLog(@"🧪 [测试] 触发了兜底机制: %@", error.localizedDescription);
        }
        NSLog(@"🧪 [测试] ========== 超时测试完成 ==========\n");
    }];
}

- (void)testCachedCoordinate {
    CLLocationCoordinate2D cached = [[LocationManager sharedManager] getCachedCoordinate];
    NSLog(@"🧪 [测试] ========== 缓存坐标查看 ==========");
    NSLog(@"🧪 [测试] 当前缓存坐标: %.6f, %.6f", cached.latitude, cached.longitude);
    NSLog(@"🧪 [测试] 坐标有效性: %@", CLLocationCoordinate2DIsValid(cached) ? @"有效" : @"无效");
    NSLog(@"🧪 [测试] ========== 缓存坐标查看完成 ==========\n");
}

- (void)clearConsole {
    NSLog(@"\n\n\n\n\n\n\n\n\n\n");
    NSLog(@"🧹 [测试] ========== 控制台已清空 ==========");
    NSLog(@"🧹 [测试] 可以开始新的测试了");
    NSLog(@"🧹 [测试] ========================================\n");
}

@end
