#import "LocationManager.h"

static NSString * const kCachedCoordinateKey = @"CachedCoordinate";
static const NSTimeInterval kLocationTimeout = 8.0; // 8秒超时

/// 单次定位请求对象
@interface LocationRequest : NSObject
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) LocationCompletionBlock completion;
@property (nonatomic, strong) NSTimer *timeoutTimer;
@property (nonatomic, assign) NSTimeInterval startTime;
@end

@implementation LocationRequest
@end

@interface LocationManager () <CLLocationManagerDelegate>
@property (nonatomic, strong) NSMutableArray<LocationRequest *> *activeRequests;
@property (nonatomic, assign) CLLocationCoordinate2D cachedCoordinate;
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeRequests = [NSMutableArray array];
        _cachedCoordinate = [self loadCachedCoordinate];
    }
    return self;
}

#pragma mark - Public Methods

- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion {
    if (!completion) {
        NSLog(@"[LocationManager] completion block is nil");
        return;
    }
    
    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        NSLog(@"[LocationManager] 定位权限被拒绝，使用缓存坐标");
        completion(self.cachedCoordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
        return;
    }
    
    // 创建新的定位请求
    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];
    
    // 创建新的CLLocationManager实例
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBest;
    request.locationManager.distanceFilter = kCLDistanceFilterNone;
    
    // 设置超时定时器
    __weak typeof(self) weakSelf = self;
    __weak typeof(request) weakRequest = request;
    request.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:kLocationTimeout repeats:NO block:^(NSTimer * _Nonnull timer) {
        [weakSelf handleLocationTimeout:weakRequest];
    }];
    
    // 添加到活跃请求列表
    [self.activeRequests addObject:request];
    
    NSLog(@"[LocationManager] 开始新的定位请求，当前活跃请求数: %lu", (unsigned long)self.activeRequests.count);
    
    // 请求权限（如果需要）并开始定位
    if (status == kCLAuthorizationStatusNotDetermined) {
        [request.locationManager requestWhenInUseAuthorization];
    } else {
        [request.locationManager startUpdatingLocation];
    }
}

- (CLLocationCoordinate2D)getCachedCoordinate {
    return self.cachedCoordinate;
}

#pragma mark - Private Methods

- (CLLocationCoordinate2D)loadCachedCoordinate {
    NSData *data = [[NSUserDefaults standardUserDefaults] objectForKey:kCachedCoordinateKey];
    if (data) {
        CLLocationCoordinate2D coordinate;
        [data getBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        if (CLLocationCoordinate2DIsValid(coordinate)) {
            NSLog(@"[LocationManager] 加载缓存坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
            return coordinate;
        }
    }
    // 返回默认坐标（北京）
    return CLLocationCoordinate2DMake(39.9042, 116.4074);
}

- (void)saveCachedCoordinate:(CLLocationCoordinate2D)coordinate {
    if (CLLocationCoordinate2DIsValid(coordinate)) {
        NSData *data = [NSData dataWithBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:kCachedCoordinateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        self.cachedCoordinate = coordinate;
        NSLog(@"[LocationManager] 保存缓存坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
    }
}

- (void)handleLocationTimeout:(LocationRequest *)request {
    if (!request || ![self.activeRequests containsObject:request]) {
        return;
    }
    
    NSLog(@"[LocationManager] 定位超时，使用缓存坐标兜底");
    
    // 停止定位
    [request.locationManager stopUpdatingLocation];
    
    // 使用缓存坐标回调
    if (request.completion) {
        request.completion(self.cachedCoordinate, [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, using cached coordinate"}]);
    }
    
    // 清理请求
    [self cleanupRequest:request];
}

- (void)cleanupRequest:(LocationRequest *)request {
    if (!request) return;
    
    // 取消定时器
    if (request.timeoutTimer) {
        [request.timeoutTimer invalidate];
        request.timeoutTimer = nil;
    }
    
    // 停止定位
    [request.locationManager stopUpdatingLocation];
    request.locationManager.delegate = nil;
    
    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];
    
    NSLog(@"[LocationManager] 清理定位请求，剩余活跃请求数: %lu", (unsigned long)self.activeRequests.count);
}

- (LocationRequest *)findRequestForLocationManager:(CLLocationManager *)manager {
    for (LocationRequest *request in self.activeRequests) {
        if (request.locationManager == manager) {
            return request;
        }
    }
    return nil;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) return;
    
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) return;
    
    NSLog(@"[LocationManager] 获取到新位置: %.6f, %.6f, 精度: %.1fm", 
          location.coordinate.latitude, location.coordinate.longitude, location.horizontalAccuracy);
    
    // 检查位置精度，如果精度太差则继续等待
    if (location.horizontalAccuracy > 100) {
        NSLog(@"[LocationManager] 位置精度较差(%.1fm)，继续等待更精确的位置", location.horizontalAccuracy);
        return;
    }
    
    // 保存到缓存
    [self saveCachedCoordinate:location.coordinate];
    
    // 回调成功结果
    if (request.completion) {
        request.completion(location.coordinate, nil);
    }
    
    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) return;
    
    NSLog(@"[LocationManager] 定位失败: %@，使用缓存坐标", error);
    
    // 使用缓存坐标回调
    if (request.completion) {
        request.completion(self.cachedCoordinate, error);
    }
    
    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    NSLog(@"[LocationManager] 定位权限状态变化: %d", (int)status);
    
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) return;
    
    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            NSLog(@"[LocationManager] 定位权限已授权，开始定位");
            [manager startUpdatingLocation];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            NSLog(@"[LocationManager] 定位权限被拒绝或受限，使用缓存坐标");
            if (request.completion) {
                request.completion(self.cachedCoordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
            }
            [self cleanupRequest:request];
            break;
        case kCLAuthorizationStatusNotDetermined:
            NSLog(@"[LocationManager] 定位权限未确定");
            break;
        default:
            break;
    }
}

@end
