#import "LocationManager.h"

static NSString * const kCachedCoordinateKey = @"CachedCoordinate";
static const NSTimeInterval kLocationTimeout = 8.0; // 8秒超时

/// 单次定位请求对象
@interface LocationRequest : NSObject
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) LocationCompletionBlock completion;
@property (nonatomic, strong) NSTimer *timeoutTimer;
@property (nonatomic, assign) NSTimeInterval startTime;
@end

@implementation LocationRequest
@end

@interface LocationManager () <CLLocationManagerDelegate>
@property (nonatomic, strong) NSMutableArray<LocationRequest *> *activeRequests;
@property (nonatomic, assign) CLLocationCoordinate2D cachedCoordinate;
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeRequests = [NSMutableArray array];
        _cachedCoordinate = [self loadCachedCoordinate];
    }
    return self;
}

#pragma mark - Public Methods

- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion {
    NSLog(@"🎯 [LocationManager] ========== 开始新的埋点定位请求 ==========");
    NSLog(@"🎯 [LocationManager] 请求时间: %@", [NSDate date]);

    if (!completion) {
        NSLog(@"❌ [LocationManager] completion block is nil - 请求被拒绝");
        return;
    }

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    NSLog(@"🔐 [LocationManager] 当前定位权限状态: %d (%@)", (int)status, [self authorizationStatusString:status]);

    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        NSLog(@"❌ [LocationManager] 定位权限被拒绝，使用缓存坐标兜底");
        NSLog(@"📍 [LocationManager] 缓存坐标: %.6f, %.6f", self.cachedCoordinate.latitude, self.cachedCoordinate.longitude);
        completion(self.cachedCoordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
        NSLog(@"🎯 [LocationManager] ========== 埋点定位请求结束(权限拒绝) ==========\n");
        return;
    }

    // 创建新的定位请求
    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    NSLog(@"🆕 [LocationManager] 创建新的CLLocationManager实例 (避免单例+block问题)");
    // 创建新的CLLocationManager实例
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBest;
    request.locationManager.distanceFilter = kCLDistanceFilterNone;

    NSLog(@"⏰ [LocationManager] 设置%.1f秒超时定时器 (确保埋点兜底)", kLocationTimeout);
    // 设置超时定时器
    __weak typeof(self) weakSelf = self;
    __weak typeof(request) weakRequest = request;
    request.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:kLocationTimeout repeats:NO block:^(NSTimer * _Nonnull timer) {
        NSLog(@"⏰ [LocationManager] 定时器触发 - 开始超时处理");
        [weakSelf handleLocationTimeout:weakRequest];
    }];

    // 添加到活跃请求列表
    [self.activeRequests addObject:request];

    NSLog(@"📊 [LocationManager] 当前活跃定位请求数: %lu (每个埋点独立请求)", (unsigned long)self.activeRequests.count);

    // 请求权限（如果需要）并开始定位
    if (status == kCLAuthorizationStatusNotDetermined) {
        NSLog(@"🔐 [LocationManager] 权限未确定，请求When-In-Use权限");
        [request.locationManager requestWhenInUseAuthorization];
    } else {
        NSLog(@"🚀 [LocationManager] 权限已授权，立即开始定位");
        [request.locationManager startUpdatingLocation];
    }
}

- (CLLocationCoordinate2D)getCachedCoordinate {
    return self.cachedCoordinate;
}

#pragma mark - Private Methods

- (CLLocationCoordinate2D)loadCachedCoordinate {
    NSLog(@"💾 [LocationManager] 尝试加载缓存坐标...");
    NSData *data = [[NSUserDefaults standardUserDefaults] objectForKey:kCachedCoordinateKey];
    if (data) {
        CLLocationCoordinate2D coordinate;
        [data getBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        if (CLLocationCoordinate2DIsValid(coordinate)) {
            NSLog(@"✅ [LocationManager] 成功加载缓存坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
            return coordinate;
        } else {
            NSLog(@"❌ [LocationManager] 缓存坐标无效，使用默认坐标");
        }
    } else {
        NSLog(@"❌ [LocationManager] 无缓存坐标数据，使用默认坐标");
    }
    // 返回默认坐标（北京）
    CLLocationCoordinate2D defaultCoordinate = CLLocationCoordinate2DMake(39.9042, 116.4074);
    NSLog(@"🏠 [LocationManager] 使用默认坐标(北京): %.6f, %.6f", defaultCoordinate.latitude, defaultCoordinate.longitude);
    return defaultCoordinate;
}

- (void)saveCachedCoordinate:(CLLocationCoordinate2D)coordinate {
    if (CLLocationCoordinate2DIsValid(coordinate)) {
        NSData *data = [NSData dataWithBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:kCachedCoordinateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        self.cachedCoordinate = coordinate;
        NSLog(@"💾 [LocationManager] ✅ 成功保存缓存坐标: %.6f, %.6f (用于下次兜底)", coordinate.latitude, coordinate.longitude);
    } else {
        NSLog(@"❌ [LocationManager] 坐标无效，不保存缓存: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
    }
}

- (void)handleLocationTimeout:(LocationRequest *)request {
    if (!request || ![self.activeRequests containsObject:request]) {
        NSLog(@"⏰ [LocationManager] 超时处理被忽略 - 请求已不存在或已完成");
        return;
    }

    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;
    NSLog(@"⏰ [LocationManager] ========== 定位超时处理 ==========");
    NSLog(@"⏰ [LocationManager] 实际耗时: %.1f秒 (超时阈值: %.1f秒)", elapsedTime, kLocationTimeout);
    NSLog(@"🛡️ [LocationManager] 启动兜底机制 - 使用缓存坐标确保埋点完整性");
    NSLog(@"📍 [LocationManager] 兜底坐标: %.6f, %.6f", self.cachedCoordinate.latitude, self.cachedCoordinate.longitude);

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    // 使用缓存坐标回调
    if (request.completion) {
        NSError *timeoutError = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, using cached coordinate"}];
        request.completion(self.cachedCoordinate, timeoutError);
        NSLog(@"✅ [LocationManager] 兜底回调已执行 - 埋点数据已提供");
    }

    // 清理请求
    [self cleanupRequest:request];
    NSLog(@"⏰ [LocationManager] ========== 超时处理完成 ==========\n");
}

- (void)cleanupRequest:(LocationRequest *)request {
    if (!request) {
        NSLog(@"🧹 [LocationManager] 清理请求被忽略 - 请求对象为空");
        return;
    }

    NSLog(@"🧹 [LocationManager] 开始清理定位请求...");

    // 取消定时器
    if (request.timeoutTimer) {
        [request.timeoutTimer invalidate];
        request.timeoutTimer = nil;
        NSLog(@"🧹 [LocationManager] ✅ 定时器已取消");
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];
    request.locationManager.delegate = nil;
    NSLog(@"🧹 [LocationManager] ✅ CLLocationManager已停止并清理");

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];

    NSLog(@"🧹 [LocationManager] ✅ 请求清理完成，剩余活跃请求数: %lu", (unsigned long)self.activeRequests.count);
}

- (LocationRequest *)findRequestForLocationManager:(CLLocationManager *)manager {
    for (LocationRequest *request in self.activeRequests) {
        if (request.locationManager == manager) {
            return request;
        }
    }
    return nil;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) {
        NSLog(@"📍 [LocationManager] 收到空位置数据，忽略");
        return;
    }

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        NSLog(@"📍 [LocationManager] 找不到对应的定位请求，忽略位置更新");
        return;
    }

    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;
    NSLog(@"📍 [LocationManager] ========== 收到位置更新 ==========");
    NSLog(@"📍 [LocationManager] 位置: %.6f, %.6f", location.coordinate.latitude, location.coordinate.longitude);
    NSLog(@"📍 [LocationManager] 精度: %.1fm", location.horizontalAccuracy);
    NSLog(@"📍 [LocationManager] 耗时: %.1f秒", elapsedTime);

    // 检查位置精度，如果精度太差则继续等待
    if (location.horizontalAccuracy > 100) {
        NSLog(@"⚠️ [LocationManager] 位置精度较差(%.1fm > 100m)，继续等待更精确的位置", location.horizontalAccuracy);
        return;
    }

    NSLog(@"✅ [LocationManager] 位置精度良好(%.1fm ≤ 100m)，采用此位置", location.horizontalAccuracy);

    // 保存到缓存
    [self saveCachedCoordinate:location.coordinate];

    // 回调成功结果
    if (request.completion) {
        request.completion(location.coordinate, nil);
        NSLog(@"✅ [LocationManager] 实时定位回调已执行 - 埋点获得精确坐标");
    }

    // 清理请求
    [self cleanupRequest:request];
    NSLog(@"📍 [LocationManager] ========== 定位成功完成 ==========\n");
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        NSLog(@"❌ [LocationManager] 找不到对应的定位请求，忽略定位失败");
        return;
    }

    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;
    NSLog(@"❌ [LocationManager] ========== 定位失败处理 ==========");
    NSLog(@"❌ [LocationManager] 失败原因: %@", error.localizedDescription);
    NSLog(@"❌ [LocationManager] 失败耗时: %.1f秒", elapsedTime);
    NSLog(@"🛡️ [LocationManager] 启动兜底机制 - 使用缓存坐标确保埋点完整性");
    NSLog(@"📍 [LocationManager] 兜底坐标: %.6f, %.6f", self.cachedCoordinate.latitude, self.cachedCoordinate.longitude);

    // 使用缓存坐标回调
    if (request.completion) {
        request.completion(self.cachedCoordinate, error);
        NSLog(@"✅ [LocationManager] 失败兜底回调已执行 - 埋点数据已提供");
    }

    // 清理请求
    [self cleanupRequest:request];
    NSLog(@"❌ [LocationManager] ========== 定位失败处理完成 ==========\n");
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    NSLog(@"🔐 [LocationManager] ========== 权限状态变化 ==========");
    NSLog(@"🔐 [LocationManager] 新权限状态: %d (%@)", (int)status, [self authorizationStatusString:status]);

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        NSLog(@"🔐 [LocationManager] 找不到对应的定位请求，忽略权限变化");
        return;
    }

    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            NSLog(@"✅ [LocationManager] 定位权限已授权，立即开始定位");
            [manager startUpdatingLocation];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            NSLog(@"❌ [LocationManager] 定位权限被拒绝或受限");
            NSLog(@"🛡️ [LocationManager] 启动兜底机制 - 使用缓存坐标");
            NSLog(@"📍 [LocationManager] 兜底坐标: %.6f, %.6f", self.cachedCoordinate.latitude, self.cachedCoordinate.longitude);
            if (request.completion) {
                request.completion(self.cachedCoordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
                NSLog(@"✅ [LocationManager] 权限拒绝兜底回调已执行");
            }
            [self cleanupRequest:request];
            break;
        case kCLAuthorizationStatusNotDetermined:
            NSLog(@"⏳ [LocationManager] 定位权限未确定，等待用户选择");
            break;
        default:
            NSLog(@"❓ [LocationManager] 未知权限状态: %d", (int)status);
            break;
    }
    NSLog(@"🔐 [LocationManager] ========== 权限处理完成 ==========\n");
}

#pragma mark - Helper Methods

- (NSString *)authorizationStatusString:(CLAuthorizationStatus)status {
    switch (status) {
        case kCLAuthorizationStatusNotDetermined:
            return @"未确定";
        case kCLAuthorizationStatusRestricted:
            return @"受限";
        case kCLAuthorizationStatusDenied:
            return @"拒绝";
        case kCLAuthorizationStatusAuthorizedAlways:
            return @"始终允许";
        case kCLAuthorizationStatusAuthorizedWhenInUse:
            return @"使用时允许";
        default:
            return [NSString stringWithFormat:@"未知(%d)", (int)status];
    }
}

@end
