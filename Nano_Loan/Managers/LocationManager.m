#import "LocationManager.h"

static NSString * const kCachedCoordinateKey = @"CachedCoordinate";
static const NSTimeInterval kLocationTimeout = 20.0; // 20秒超时，给GPS充足时间获得厘米级精度
static const CLLocationAccuracy kBestAccuracy = 1.0; // 最佳精度1米（厘米级目标）
static const CLLocationAccuracy kGoodAccuracy = 3.0; // 良好精度3米
static const CLLocationAccuracy kAcceptableAccuracy = 10.0; // 可接受精度10米（最后兜底）
static const NSTimeInterval kLocationMaxAge = 2.0; // 位置数据最大有效期2秒（确保新鲜度）
static const NSTimeInterval kMinWaitTime = 5.0; // 最少等待5秒让GPS稳定

/// 单次定位请求对象
@interface LocationRequest : NSObject
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) LocationCompletionBlock completion;
@property (nonatomic, strong) NSTimer *timeoutTimer;
@property (nonatomic, assign) NSTimeInterval startTime;
@property (nonatomic, strong) CLLocation *bestLocation; // 当前最佳位置
@property (nonatomic, assign) BOOL hasReceivedFirstLocation; // 是否已收到第一个位置
@property (nonatomic, assign) NSInteger locationUpdateCount; // 位置更新次数
@end

@implementation LocationRequest
@end

@interface LocationManager () <CLLocationManagerDelegate>
@property (nonatomic, strong) NSMutableArray<LocationRequest *> *activeRequests;
@property (nonatomic, assign) CLLocationCoordinate2D cachedCoordinate;
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeRequests = [NSMutableArray array];
        _cachedCoordinate = [self loadCachedCoordinate];
    }
    return self;
}

#pragma mark - Public Methods

- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion {
    NSLog(@"🎯 [LocationManager] ========== 开始新的埋点定位请求 ==========");
    NSLog(@"🎯 [LocationManager] 请求时间: %@", [NSDate date]);

    if (!completion) {
        NSLog(@"❌ [LocationManager] completion block is nil - 请求被拒绝");
        return;
    }

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    NSLog(@"🔐 [LocationManager] 当前定位权限状态: %d (%@)", (int)status, [self authorizationStatusString:status]);

    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        NSLog(@"❌ [LocationManager] 定位权限被拒绝，使用缓存坐标兜底");
        NSLog(@"📍 [LocationManager] 缓存坐标: %.6f, %.6f", self.cachedCoordinate.latitude, self.cachedCoordinate.longitude);
        completion(self.cachedCoordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
        NSLog(@"🎯 [LocationManager] ========== 埋点定位请求结束(权限拒绝) ==========\n");
        return;
    }

    // 创建新的定位请求
    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    NSLog(@"🆕 [LocationManager] 创建新的CLLocationManager实例 (避免单例+block问题)");
    // 创建新的CLLocationManager实例，配置最高精度
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;

    // 设置最高精度配置
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBestForNavigation; // 导航级最高精度
    request.locationManager.distanceFilter = 0.1; // 0.1米距离过滤，捕获微小移动

    // iOS 14+ 临时精确位置权限
    if (@available(iOS 14.0, *)) {
        if (request.locationManager.accuracyAuthorization == CLAccuracyAuthorizationReducedAccuracy) {
            NSLog(@"🔐 [LocationManager] 检测到精度受限，请求临时完整精度");
            [request.locationManager requestTemporaryFullAccuracyAuthorizationWithPurposeKey:@"tracking" completion:^(NSError * _Nullable error) {
                if (error) {
                    NSLog(@"⚠️ [LocationManager] 临时精度权限请求失败: %@", error.localizedDescription);
                } else {
                    NSLog(@"✅ [LocationManager] 临时精度权限请求成功");
                }
            }];
        }
    }

    NSLog(@"🎯 [LocationManager] 精度配置: 导航级最高精度 + 0.1米距离过滤");

    NSLog(@"⏰ [LocationManager] 设置%.1f秒超时定时器 (确保埋点兜底)", kLocationTimeout);
    // 设置超时定时器
    __weak typeof(self) weakSelf = self;
    __weak typeof(request) weakRequest = request;
    request.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:kLocationTimeout repeats:NO block:^(NSTimer * _Nonnull timer) {
        NSLog(@"⏰ [LocationManager] 定时器触发 - 开始超时处理");
        [weakSelf handleLocationTimeout:weakRequest];
    }];

    // 添加到活跃请求列表
    [self.activeRequests addObject:request];

    NSLog(@"📊 [LocationManager] 当前活跃定位请求数: %lu (每个埋点独立请求)", (unsigned long)self.activeRequests.count);

    // 请求权限（如果需要）并开始定位
    if (status == kCLAuthorizationStatusNotDetermined) {
        NSLog(@"🔐 [LocationManager] 权限未确定，请求When-In-Use权限");
        [request.locationManager requestWhenInUseAuthorization];
    } else {
        NSLog(@"🚀 [LocationManager] 权限已授权，立即开始定位");
        [request.locationManager startUpdatingLocation];
    }
}

- (CLLocationCoordinate2D)getCachedCoordinate {
    return self.cachedCoordinate;
}

#pragma mark - Private Methods

- (CLLocationCoordinate2D)loadCachedCoordinate {
    NSLog(@"💾 [LocationManager] 尝试加载缓存坐标...");
    NSData *data = [[NSUserDefaults standardUserDefaults] objectForKey:kCachedCoordinateKey];
    if (data) {
        CLLocationCoordinate2D coordinate;
        [data getBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        if (CLLocationCoordinate2DIsValid(coordinate)) {
            NSLog(@"✅ [LocationManager] 成功加载缓存坐标: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
            return coordinate;
        } else {
            NSLog(@"❌ [LocationManager] 缓存坐标无效，使用默认坐标");
        }
    } else {
        NSLog(@"❌ [LocationManager] 无缓存坐标数据，使用默认坐标");
    }
    // 返回默认坐标（北京）
    CLLocationCoordinate2D defaultCoordinate = CLLocationCoordinate2DMake(39.9042, 116.4074);
    NSLog(@"🏠 [LocationManager] 使用默认坐标(北京): %.6f, %.6f", defaultCoordinate.latitude, defaultCoordinate.longitude);
    return defaultCoordinate;
}

- (void)saveCachedCoordinate:(CLLocationCoordinate2D)coordinate {
    if (CLLocationCoordinate2DIsValid(coordinate)) {
        NSData *data = [NSData dataWithBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:kCachedCoordinateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        self.cachedCoordinate = coordinate;
        NSLog(@"💾 [LocationManager] ✅ 成功保存缓存坐标: %.6f, %.6f (用于下次兜底)", coordinate.latitude, coordinate.longitude);
    } else {
        NSLog(@"❌ [LocationManager] 坐标无效，不保存缓存: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
    }
}

- (void)handleLocationTimeout:(LocationRequest *)request {
    if (!request || ![self.activeRequests containsObject:request]) {
        NSLog(@"⏰ [LocationManager] 超时处理被忽略 - 请求已不存在或已完成");
        return;
    }

    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;
    NSLog(@"⏰ [LocationManager] ========== 定位超时处理 ==========");
    NSLog(@"⏰ [LocationManager] 实际耗时: %.1f秒 (超时阈值: %.1f秒)", elapsedTime, kLocationTimeout);
    NSLog(@"⏰ [LocationManager] 位置更新次数: %ld", (long)request.locationUpdateCount);

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    CLLocationCoordinate2D finalCoordinate;
    NSError *error = nil;

    // 优先使用本次定位过程中获得的最佳位置
    if (request.bestLocation && CLLocationCoordinate2DIsValid(request.bestLocation.coordinate)) {
        finalCoordinate = request.bestLocation.coordinate;
        NSLog(@"🎯 [LocationManager] 使用本次最佳位置: %.8f, %.8f (精度: %.2fm)",
              finalCoordinate.latitude, finalCoordinate.longitude, request.bestLocation.horizontalAccuracy);

        // 更新缓存为最新的最佳位置
        [self saveCachedCoordinate:finalCoordinate];

        // 如果精度还不错，不算错误
        if (request.bestLocation.horizontalAccuracy <= kAcceptableAccuracy) {
            NSLog(@"✅ [LocationManager] 最佳位置精度可接受(%.2fm ≤ %.1fm)，视为成功",
                  request.bestLocation.horizontalAccuracy, kAcceptableAccuracy);
        } else {
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Timeout with low accuracy: %.1fm", request.bestLocation.horizontalAccuracy]}];
        }
    } else {
        // 使用缓存坐标兜底
        finalCoordinate = self.cachedCoordinate;
        error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, using cached coordinate"}];
        NSLog(@"🛡️ [LocationManager] 无可用新位置，使用缓存坐标兜底: %.8f, %.8f",
              finalCoordinate.latitude, finalCoordinate.longitude);
    }

    // 回调结果
    if (request.completion) {
        request.completion(finalCoordinate, error);
        NSLog(@"✅ [LocationManager] 超时处理回调已执行 - 埋点数据已提供");
    }

    // 清理请求
    [self cleanupRequest:request];
    NSLog(@"⏰ [LocationManager] ========== 超时处理完成 ==========\n");
}

- (void)cleanupRequest:(LocationRequest *)request {
    if (!request) {
        NSLog(@"🧹 [LocationManager] 清理请求被忽略 - 请求对象为空");
        return;
    }

    NSLog(@"🧹 [LocationManager] 开始清理定位请求...");

    // 取消定时器
    if (request.timeoutTimer) {
        [request.timeoutTimer invalidate];
        request.timeoutTimer = nil;
        NSLog(@"🧹 [LocationManager] ✅ 定时器已取消");
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];
    request.locationManager.delegate = nil;
    NSLog(@"🧹 [LocationManager] ✅ CLLocationManager已停止并清理");

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];

    NSLog(@"🧹 [LocationManager] ✅ 请求清理完成，剩余活跃请求数: %lu", (unsigned long)self.activeRequests.count);
}

- (LocationRequest *)findRequestForLocationManager:(CLLocationManager *)manager {
    for (LocationRequest *request in self.activeRequests) {
        if (request.locationManager == manager) {
            return request;
        }
    }
    return nil;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) {
        NSLog(@"📍 [LocationManager] 收到空位置数据，忽略");
        return;
    }

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        NSLog(@"📍 [LocationManager] 找不到对应的定位请求，忽略位置更新");
        return;
    }

    // 检查位置数据的新鲜度
    NSTimeInterval locationAge = -[location.timestamp timeIntervalSinceNow];
    if (locationAge > kLocationMaxAge) {
        NSLog(@"⚠️ [LocationManager] 位置数据过旧(%.1f秒)，忽略", locationAge);
        return;
    }

    // 检查位置精度有效性
    if (location.horizontalAccuracy < 0) {
        NSLog(@"⚠️ [LocationManager] 位置精度无效(%.1fm)，忽略", location.horizontalAccuracy);
        return;
    }

    request.locationUpdateCount++;
    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;

    NSLog(@"📍 [LocationManager] ========== 收到位置更新 #%ld ==========", (long)request.locationUpdateCount);
    NSLog(@"📍 [LocationManager] 位置: %.8f, %.8f (8位小数精度)", location.coordinate.latitude, location.coordinate.longitude);
    NSLog(@"📍 [LocationManager] 精度: %.2fm", location.horizontalAccuracy);
    NSLog(@"📍 [LocationManager] 数据年龄: %.1f秒", locationAge);
    NSLog(@"📍 [LocationManager] 耗时: %.1f秒", elapsedTime);

    // 智能精度判断逻辑
    BOOL shouldAcceptLocation = [self shouldAcceptLocation:location forRequest:request elapsedTime:elapsedTime];

    if (!shouldAcceptLocation) {
        // 更新最佳位置记录
        if (!request.bestLocation || location.horizontalAccuracy < request.bestLocation.horizontalAccuracy) {
            request.bestLocation = location;
            NSLog(@"🔄 [LocationManager] 更新最佳位置记录: %.2fm精度", location.horizontalAccuracy);
        }
        return;
    }

    // 选择最终位置
    CLLocation *finalLocation = request.bestLocation && request.bestLocation.horizontalAccuracy < location.horizontalAccuracy ? request.bestLocation : location;

    NSLog(@"✅ [LocationManager] 采用最终位置: %.8f, %.8f", finalLocation.coordinate.latitude, finalLocation.coordinate.longitude);
    NSLog(@"✅ [LocationManager] 最终精度: %.2fm", finalLocation.horizontalAccuracy);

    // 保存到缓存
    [self saveCachedCoordinate:finalLocation.coordinate];

    // 回调成功结果
    if (request.completion) {
        request.completion(finalLocation.coordinate, nil);
        NSLog(@"✅ [LocationManager] 高精度定位回调已执行 - 埋点获得%.2fm精度坐标", finalLocation.horizontalAccuracy);
    }

    // 清理请求
    [self cleanupRequest:request];
    NSLog(@"📍 [LocationManager] ========== 高精度定位完成 ==========\n");
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        NSLog(@"❌ [LocationManager] 找不到对应的定位请求，忽略定位失败");
        return;
    }

    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;
    NSLog(@"❌ [LocationManager] ========== 定位失败处理 ==========");
    NSLog(@"❌ [LocationManager] 失败原因: %@", error.localizedDescription);
    NSLog(@"❌ [LocationManager] 失败耗时: %.1f秒", elapsedTime);
    NSLog(@"🛡️ [LocationManager] 启动兜底机制 - 使用缓存坐标确保埋点完整性");
    NSLog(@"📍 [LocationManager] 兜底坐标: %.6f, %.6f", self.cachedCoordinate.latitude, self.cachedCoordinate.longitude);

    // 使用缓存坐标回调
    if (request.completion) {
        request.completion(self.cachedCoordinate, error);
        NSLog(@"✅ [LocationManager] 失败兜底回调已执行 - 埋点数据已提供");
    }

    // 清理请求
    [self cleanupRequest:request];
    NSLog(@"❌ [LocationManager] ========== 定位失败处理完成 ==========\n");
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    NSLog(@"🔐 [LocationManager] ========== 权限状态变化 ==========");
    NSLog(@"🔐 [LocationManager] 新权限状态: %d (%@)", (int)status, [self authorizationStatusString:status]);

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        NSLog(@"🔐 [LocationManager] 找不到对应的定位请求，忽略权限变化");
        return;
    }

    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            NSLog(@"✅ [LocationManager] 定位权限已授权，立即开始定位");
            [manager startUpdatingLocation];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            NSLog(@"❌ [LocationManager] 定位权限被拒绝或受限");
            NSLog(@"🛡️ [LocationManager] 启动兜底机制 - 使用缓存坐标");
            NSLog(@"📍 [LocationManager] 兜底坐标: %.6f, %.6f", self.cachedCoordinate.latitude, self.cachedCoordinate.longitude);
            if (request.completion) {
                request.completion(self.cachedCoordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
                NSLog(@"✅ [LocationManager] 权限拒绝兜底回调已执行");
            }
            [self cleanupRequest:request];
            break;
        case kCLAuthorizationStatusNotDetermined:
            NSLog(@"⏳ [LocationManager] 定位权限未确定，等待用户选择");
            break;
        default:
            NSLog(@"❓ [LocationManager] 未知权限状态: %d", (int)status);
            break;
    }
    NSLog(@"🔐 [LocationManager] ========== 权限处理完成 ==========\n");
}

#pragma mark - Helper Methods

/// 智能判断是否应该接受当前位置
- (BOOL)shouldAcceptLocation:(CLLocation *)location forRequest:(LocationRequest *)request elapsedTime:(NSTimeInterval)elapsedTime {
    // 第一次收到位置
    if (!request.hasReceivedFirstLocation) {
        request.hasReceivedFirstLocation = YES;
        NSLog(@"🎯 [LocationManager] 首次收到位置，开始精度优化流程");
    }

    // 立即接受的条件：厘米级精度
    if (location.horizontalAccuracy <= kBestAccuracy) {
        NSLog(@"🏆 [LocationManager] 达到最佳精度(%.2fm ≤ %.1fm)，立即采用", location.horizontalAccuracy, kBestAccuracy);
        return YES;
    }

    // 良好精度 + 等待时间足够
    if (location.horizontalAccuracy <= kGoodAccuracy && elapsedTime >= kMinWaitTime) {
        NSLog(@"✅ [LocationManager] 达到良好精度(%.2fm ≤ %.1fm) + 等待充足(%.1fs ≥ %.1fs)，采用",
              location.horizontalAccuracy, kGoodAccuracy, elapsedTime, kMinWaitTime);
        return YES;
    }

    // 可接受精度 + 等待时间较长
    if (location.horizontalAccuracy <= kAcceptableAccuracy && elapsedTime >= (kMinWaitTime * 2)) {
        NSLog(@"⚡ [LocationManager] 达到可接受精度(%.2fm ≤ %.1fm) + 等待较长(%.1fs ≥ %.1fs)，采用",
              location.horizontalAccuracy, kAcceptableAccuracy, elapsedTime, kMinWaitTime * 2);
        return YES;
    }

    // 超过最大等待时间，使用最佳可用位置
    if (elapsedTime >= (kLocationTimeout * 0.8)) { // 80%超时时间
        NSLog(@"⏰ [LocationManager] 接近超时(%.1fs ≥ %.1fs)，准备使用最佳可用位置",
              elapsedTime, kLocationTimeout * 0.8);
        return YES;
    }

    // 继续等待更好的精度
    NSLog(@"⏳ [LocationManager] 精度不够(%.2fm)，继续等待更高精度(目标: ≤%.1fm)",
          location.horizontalAccuracy, kGoodAccuracy);
    return NO;
}

- (NSString *)authorizationStatusString:(CLAuthorizationStatus)status {
    switch (status) {
        case kCLAuthorizationStatusNotDetermined:
            return @"未确定";
        case kCLAuthorizationStatusRestricted:
            return @"受限";
        case kCLAuthorizationStatusDenied:
            return @"拒绝";
        case kCLAuthorizationStatusAuthorizedAlways:
            return @"始终允许";
        case kCLAuthorizationStatusAuthorizedWhenInUse:
            return @"使用时允许";
        default:
            return [NSString stringWithFormat:@"未知(%d)", (int)status];
    }
}

@end
