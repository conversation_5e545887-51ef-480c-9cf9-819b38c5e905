#import "LocationManager.h"

static NSString * const kCachedCoordinateKey = @"CachedCoordinate";
static const NSTimeInterval kLocationTimeout = 20.0; // 20秒超时，给GPS充足时间获得厘米级精度
static const CLLocationAccuracy kBestAccuracy = 1.0; // 最佳精度1米（厘米级目标）
static const CLLocationAccuracy kGoodAccuracy = 3.0; // 良好精度3米
static const CLLocationAccuracy kAcceptableAccuracy = 10.0; // 可接受精度10米（最后兜底）
static const NSTimeInterval kLocationMaxAge = 2.0; // 位置数据最大有效期2秒（确保新鲜度）
static const NSTimeInterval kMinWaitTime = 5.0; // 最少等待5秒让GPS稳定

/// 单次定位请求对象
@interface LocationRequest : NSObject
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) LocationCompletionBlock completion;
@property (nonatomic, strong) NSTimer *timeoutTimer;
@property (nonatomic, assign) NSTimeInterval startTime;
@property (nonatomic, strong) CLLocation *bestLocation; // 当前最佳位置
@property (nonatomic, assign) BOOL hasReceivedFirstLocation; // 是否已收到第一个位置
@property (nonatomic, assign) NSInteger locationUpdateCount; // 位置更新次数
@end

@implementation LocationRequest
@end

@interface LocationManager () <CLLocationManagerDelegate>
@property (nonatomic, strong) NSMutableArray<LocationRequest *> *activeRequests;
@property (nonatomic, assign) CLLocationCoordinate2D cachedCoordinate;
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeRequests = [NSMutableArray array];
        _cachedCoordinate = [self loadCachedCoordinate];
    }
    return self;
}

#pragma mark - Public Methods

- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion {
    if (!completion) {
        return;
    }

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
        return;
    }

    // 创建新的定位请求
    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    // 创建新的CLLocationManager实例，配置最高精度
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBestForNavigation;
    request.locationManager.distanceFilter = 0.1;

    // iOS 14+ 临时精确位置权限
    if (@available(iOS 14.0, *)) {
        if (request.locationManager.accuracyAuthorization == CLAccuracyAuthorizationReducedAccuracy) {
            [request.locationManager requestTemporaryFullAccuracyAuthorizationWithPurposeKey:@"tracking" completion:nil];
        }
    }

    // 设置超时定时器
    __weak typeof(self) weakSelf = self;
    __weak typeof(request) weakRequest = request;
    request.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:kLocationTimeout repeats:NO block:^(NSTimer * _Nonnull timer) {
        [weakSelf handleLocationTimeout:weakRequest];
    }];

    // 添加到活跃请求列表
    [self.activeRequests addObject:request];

    // 请求权限（如果需要）并开始定位
    if (status == kCLAuthorizationStatusNotDetermined) {
        [request.locationManager requestWhenInUseAuthorization];
    } else {
        [request.locationManager startUpdatingLocation];
    }
}

- (CLLocationCoordinate2D)getCachedCoordinate {
    return self.cachedCoordinate;
}

#pragma mark - Private Methods

- (CLLocationCoordinate2D)loadCachedCoordinate {
    NSData *data = [[NSUserDefaults standardUserDefaults] objectForKey:kCachedCoordinateKey];
    if (data) {
        CLLocationCoordinate2D coordinate;
        [data getBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        if (CLLocationCoordinate2DIsValid(coordinate)) {
            return coordinate;
        }
    }
    // 返回无效坐标，表示没有缓存数据
    return kCLLocationCoordinate2DInvalid;
}

- (void)saveCachedCoordinate:(CLLocationCoordinate2D)coordinate {
    if (CLLocationCoordinate2DIsValid(coordinate)) {
        NSData *data = [NSData dataWithBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:kCachedCoordinateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        self.cachedCoordinate = coordinate;
    }
}

- (void)handleLocationTimeout:(LocationRequest *)request {
    if (!request || ![self.activeRequests containsObject:request]) {
        return;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    CLLocationCoordinate2D finalCoordinate;
    NSError *error = nil;

    // 优先使用本次定位过程中获得的最佳位置
    if (request.bestLocation && CLLocationCoordinate2DIsValid(request.bestLocation.coordinate)) {
        finalCoordinate = request.bestLocation.coordinate;
        [self saveCachedCoordinate:finalCoordinate];

        if (request.bestLocation.horizontalAccuracy > kAcceptableAccuracy) {
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Timeout with low accuracy: %.1fm", request.bestLocation.horizontalAccuracy]}];
        }
    } else {
        // 使用缓存坐标兜底
        if (CLLocationCoordinate2DIsValid(self.cachedCoordinate)) {
            finalCoordinate = self.cachedCoordinate;
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, using cached coordinate"}];
        } else {
            finalCoordinate = kCLLocationCoordinate2DInvalid;
            error = [NSError errorWithDomain:@"LocationManager" code:-3 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, no valid coordinate available"}];
        }
    }

    // 回调结果
    if (request.completion) {
        request.completion(finalCoordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)cleanupRequest:(LocationRequest *)request {
    if (!request) {
        return;
    }

    // 取消定时器
    if (request.timeoutTimer) {
        [request.timeoutTimer invalidate];
        request.timeoutTimer = nil;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];
    request.locationManager.delegate = nil;

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];
}

- (LocationRequest *)findRequestForLocationManager:(CLLocationManager *)manager {
    for (LocationRequest *request in self.activeRequests) {
        if (request.locationManager == manager) {
            return request;
        }
    }
    return nil;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) {
        return;
    }

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    // 检查位置数据的新鲜度
    NSTimeInterval locationAge = -[location.timestamp timeIntervalSinceNow];
    if (locationAge > kLocationMaxAge) {
        return;
    }

    // 检查位置精度有效性
    if (location.horizontalAccuracy < 0) {
        return;
    }

    request.locationUpdateCount++;
    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;

    // 智能精度判断逻辑
    BOOL shouldAcceptLocation = [self shouldAcceptLocation:location forRequest:request elapsedTime:elapsedTime];

    if (!shouldAcceptLocation) {
        // 更新最佳位置记录
        if (!request.bestLocation || location.horizontalAccuracy < request.bestLocation.horizontalAccuracy) {
            request.bestLocation = location;
        }
        return;
    }

    // 选择最终位置
    CLLocation *finalLocation = request.bestLocation && request.bestLocation.horizontalAccuracy < location.horizontalAccuracy ? request.bestLocation : location;

    // 保存到缓存
    [self saveCachedCoordinate:finalLocation.coordinate];

    // 回调成功结果
    if (request.completion) {
        request.completion(finalLocation.coordinate, nil);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    // 使用缓存坐标回调
    if (request.completion) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        request.completion(coordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            [manager startUpdatingLocation];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            if (request.completion) {
                CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
                request.completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
            }
            [self cleanupRequest:request];
            break;
        case kCLAuthorizationStatusNotDetermined:
            break;
        default:
            break;
    }
}

#pragma mark - Helper Methods

/// 智能判断是否应该接受当前位置
- (BOOL)shouldAcceptLocation:(CLLocation *)location forRequest:(LocationRequest *)request elapsedTime:(NSTimeInterval)elapsedTime {
    // 第一次收到位置
    if (!request.hasReceivedFirstLocation) {
        request.hasReceivedFirstLocation = YES;
    }

    // 立即接受的条件：厘米级精度
    if (location.horizontalAccuracy <= kBestAccuracy) {
        return YES;
    }

    // 良好精度 + 等待时间足够
    if (location.horizontalAccuracy <= kGoodAccuracy && elapsedTime >= kMinWaitTime) {
        return YES;
    }

    // 可接受精度 + 等待时间较长
    if (location.horizontalAccuracy <= kAcceptableAccuracy && elapsedTime >= (kMinWaitTime * 2)) {
        return YES;
    }

    // 超过最大等待时间，使用最佳可用位置
    if (elapsedTime >= (kLocationTimeout * 0.8)) { // 80%超时时间
        return YES;
    }

    // 继续等待更好的精度
    return NO;
}



@end
