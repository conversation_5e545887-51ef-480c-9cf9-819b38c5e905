//
//  LocationManagerUsageExample.m
//  使用示例：展示如何在埋点中使用优化后的LocationManager
//

#import "LocationManager.h"

@interface LocationManagerUsageExample : NSObject
@end

@implementation LocationManagerUsageExample

// 示例1：在用户点击按钮时进行埋点
- (void)onButtonClickTracking {
    NSLog(@"🎯 [埋点测试] ========== 开始按钮点击埋点 ==========");
    NSLog(@"🎯 [埋点测试] 埋点类型: 按钮点击");
    NSLog(@"🎯 [埋点测试] 开始时间: %@", [NSDate date]);

    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSLog(@"🎯 [埋点测试] ========== 收到定位回调 ==========");

        // 构建埋点参数
        NSMutableDictionary *trackingParams = [NSMutableDictionary dictionary];
        trackingParams[@"event"] = @"button_click";
        trackingParams[@"latitude"] = @(coordinate.latitude);
        trackingParams[@"longitude"] = @(coordinate.longitude);
        trackingParams[@"timestamp"] = @([[NSDate date] timeIntervalSince1970]);

        if (error) {
            trackingParams[@"location_error"] = error.localizedDescription;
            trackingParams[@"location_source"] = @"cached";
            NSLog(@"🛡️ [埋点测试] 使用缓存坐标进行埋点 (兜底机制)");
            NSLog(@"🛡️ [埋点测试] 错误信息: %@", error.localizedDescription);
        } else {
            trackingParams[@"location_source"] = @"realtime";
            NSLog(@"✅ [埋点测试] 使用实时坐标进行埋点 (精确定位)");
        }

        NSLog(@"📊 [埋点测试] 埋点数据: %@", trackingParams);

        // 发送埋点数据
        [self sendTrackingData:trackingParams];

        NSLog(@"🎯 [埋点测试] ========== 按钮点击埋点完成 ==========\n");
    }];
}

// 示例2：在页面进入时进行埋点
- (void)onPageEnterTracking:(NSString *)pageName {
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSMutableDictionary *trackingParams = [NSMutableDictionary dictionary];
        trackingParams[@"event"] = @"page_enter";
        trackingParams[@"page_name"] = pageName;
        trackingParams[@"latitude"] = @(coordinate.latitude);
        trackingParams[@"longitude"] = @(coordinate.longitude);
        trackingParams[@"timestamp"] = @([[NSDate date] timeIntervalSince1970]);
        
        if (error) {
            trackingParams[@"location_source"] = @"cached";
        } else {
            trackingParams[@"location_source"] = @"realtime";
        }
        
        [self sendTrackingData:trackingParams];
    }];
}

// 示例3：在表单提交时进行埋点
- (void)onFormSubmitTracking:(NSDictionary *)formData {
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSMutableDictionary *trackingParams = [NSMutableDictionary dictionary];
        trackingParams[@"event"] = @"form_submit";
        trackingParams[@"form_type"] = formData[@"type"];
        trackingParams[@"latitude"] = @(coordinate.latitude);
        trackingParams[@"longitude"] = @(coordinate.longitude);
        trackingParams[@"timestamp"] = @([[NSDate date] timeIntervalSince1970]);
        
        // 即使定位失败也要确保埋点发送
        [self sendTrackingData:trackingParams];
    }];
}

// 模拟发送埋点数据的方法
- (void)sendTrackingData:(NSDictionary *)params {
    NSLog(@"📤 [埋点测试] ========== 开始发送埋点数据 ==========");
    NSLog(@"📤 [埋点测试] 埋点参数: %@", params);
    NSLog(@"📤 [埋点测试] 发送时间: %@", [NSDate date]);

    // 这里应该调用实际的埋点上报接口
    // 示例：使用NetworkManager发送数据
    // [NetworkManager postFormWithAPI:@"tracking/event" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
    //     if (error) {
    //         NSLog(@"❌ [埋点测试] 埋点上报失败: %@", error);
    //     } else {
    //         NSLog(@"✅ [埋点测试] 埋点上报成功: %@", response);
    //     }
    // }];

    // 模拟网络请求
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSLog(@"✅ [埋点测试] 模拟埋点上报成功");
        NSLog(@"📤 [埋点测试] ========== 埋点数据发送完成 ==========\n");
    });
}

@end

/*
 优化说明：
 
 1. ✅ 每次埋点都创建新的CLLocationManager实例，避免单例+block导致的回调覆盖问题
 2. ✅ 经纬度不做大于0判断，直接使用获取到的坐标值
 3. ✅ 8秒超时兜底机制，确保每个埋点都能得到坐标（实时或缓存）
 4. ✅ 只缓存经纬度坐标，不缓存逆地理编码信息
 5. ✅ 定位对象生命周期管理，确保在获取坐标前不被销毁
 6. ✅ 避免使用单例+block属性的方式，每次请求都是独立的
 7. ✅ 提前设定结束时间（通过超时定时器），不依赖定位成功时间
 
 使用方式：
 - 每次需要埋点时，直接调用 getCurrentLocationForTracking: 方法
 - 方法会自动处理权限检查、超时兜底、坐标缓存等逻辑
 - 回调中的坐标可能是实时获取的，也可能是缓存的兜底坐标
 - 通过error参数可以判断是否使用了兜底坐标
 */
