//
//  LocationManagerUsageExample.m
//  使用示例：展示如何在埋点中使用优化后的LocationManager
//

#import "LocationManager.h"

@implementation LocationManagerUsageExample

// 示例1：在用户点击按钮时进行埋点
- (void)onButtonClickTracking {
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        // 构建埋点参数
        NSMutableDictionary *trackingParams = [NSMutableDictionary dictionary];
        trackingParams[@"event"] = @"button_click";
        trackingParams[@"latitude"] = @(coordinate.latitude);
        trackingParams[@"longitude"] = @(coordinate.longitude);
        trackingParams[@"timestamp"] = @([[NSDate date] timeIntervalSince1970]);
        
        if (error) {
            trackingParams[@"location_error"] = error.localizedDescription;
            NSLog(@"[Tracking] 使用缓存坐标进行埋点: %@", trackingParams);
        } else {
            NSLog(@"[Tracking] 使用实时坐标进行埋点: %@", trackingParams);
        }
        
        // 发送埋点数据
        [self sendTrackingData:trackingParams];
    }];
}

// 示例2：在页面进入时进行埋点
- (void)onPageEnterTracking:(NSString *)pageName {
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSMutableDictionary *trackingParams = [NSMutableDictionary dictionary];
        trackingParams[@"event"] = @"page_enter";
        trackingParams[@"page_name"] = pageName;
        trackingParams[@"latitude"] = @(coordinate.latitude);
        trackingParams[@"longitude"] = @(coordinate.longitude);
        trackingParams[@"timestamp"] = @([[NSDate date] timeIntervalSince1970]);
        
        if (error) {
            trackingParams[@"location_source"] = @"cached";
        } else {
            trackingParams[@"location_source"] = @"realtime";
        }
        
        [self sendTrackingData:trackingParams];
    }];
}

// 示例3：在表单提交时进行埋点
- (void)onFormSubmitTracking:(NSDictionary *)formData {
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        NSMutableDictionary *trackingParams = [NSMutableDictionary dictionary];
        trackingParams[@"event"] = @"form_submit";
        trackingParams[@"form_type"] = formData[@"type"];
        trackingParams[@"latitude"] = @(coordinate.latitude);
        trackingParams[@"longitude"] = @(coordinate.longitude);
        trackingParams[@"timestamp"] = @([[NSDate date] timeIntervalSince1970]);
        
        // 即使定位失败也要确保埋点发送
        [self sendTrackingData:trackingParams];
    }];
}

// 模拟发送埋点数据的方法
- (void)sendTrackingData:(NSDictionary *)params {
    // 这里应该调用实际的埋点上报接口
    NSLog(@"[Tracking] 发送埋点数据: %@", params);
    
    // 示例：使用NetworkManager发送数据
    // [NetworkManager postFormWithAPI:@"tracking/event" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
    //     if (error) {
    //         NSLog(@"[Tracking] 埋点上报失败: %@", error);
    //     } else {
    //         NSLog(@"[Tracking] 埋点上报成功");
    //     }
    // }];
}

@end

/*
 优化说明：
 
 1. ✅ 每次埋点都创建新的CLLocationManager实例，避免单例+block导致的回调覆盖问题
 2. ✅ 经纬度不做大于0判断，直接使用获取到的坐标值
 3. ✅ 8秒超时兜底机制，确保每个埋点都能得到坐标（实时或缓存）
 4. ✅ 只缓存经纬度坐标，不缓存逆地理编码信息
 5. ✅ 定位对象生命周期管理，确保在获取坐标前不被销毁
 6. ✅ 避免使用单例+block属性的方式，每次请求都是独立的
 7. ✅ 提前设定结束时间（通过超时定时器），不依赖定位成功时间
 
 使用方式：
 - 每次需要埋点时，直接调用 getCurrentLocationForTracking: 方法
 - 方法会自动处理权限检查、超时兜底、坐标缓存等逻辑
 - 回调中的坐标可能是实时获取的，也可能是缓存的兜底坐标
 - 通过error参数可以判断是否使用了兜底坐标
 */
