#ifndef LocationManager_h
#define LocationManager_h

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

/// 定位完成回调
typedef void(^LocationCompletionBlock)(CLLocationCoordinate2D coordinate, NSError * _Nullable error);

/// 负责埋点定位的管理类，每次埋点都会创建新的CLLocationManager实例
@interface LocationManager : NSObject

+ (instancetype)sharedManager;

/// 为埋点获取当前位置，每次调用都会创建新的定位实例
/// @param completion 定位完成回调，5-10秒超时后会使用缓存坐标兜底
- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion;

/// 获取缓存的经纬度坐标（用于兜底）
- (CLLocationCoordinate2D)getCachedCoordinate;

@end

NS_ASSUME_NONNULL_END

#endif /* LocationManager_h */