#ifndef LocationManager_h
#define LocationManager_h

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

/// 定位完成回调
typedef void(^LocationCompletionBlock)(CLLocationCoordinate2D coordinate, NSError * _Nullable error);

/// 高精度埋点定位管理类
@interface LocationManager : NSObject

+ (instancetype)sharedManager;

/// 高精度埋点定位
/// @param completion 定位完成回调
- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion;

/// 获取缓存的经纬度坐标
- (CLLocationCoordinate2D)getCachedCoordinate;

/// 上报位置信息到服务器
- (void)reportLocationInfo;

@end

NS_ASSUME_NONNULL_END

#endif /* LocationManager_h */