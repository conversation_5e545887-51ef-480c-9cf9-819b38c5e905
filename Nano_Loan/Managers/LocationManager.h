#ifndef LocationManager_h
#define LocationManager_h

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

/// 定位完成回调
typedef void(^LocationCompletionBlock)(CLLocationCoordinate2D coordinate, NSError * _Nullable error);

/// 高精度埋点定位管理类
/// 特性：
/// - 每次埋点创建独立CLLocationManager实例，避免回调冲突
/// - 导航级最高精度配置 (kCLLocationAccuracyBestForNavigation)
/// - 智能精度优化：目标1米精度，可接受10米精度
/// - 20秒超时保护，优先使用本次最佳位置，兜底使用缓存坐标
/// - 支持iOS 14+临时完整精度权限
/// - 0.1米距离过滤，捕获微小位置变化
@interface LocationManager : NSObject

+ (instancetype)sharedManager;

/// 高精度埋点定位
/// @param completion 定位完成回调
/// - 成功：返回实时高精度坐标(目标1-3米精度)，error为nil
/// - 超时但有位置：返回本次最佳位置，error包含精度信息
/// - 完全失败：返回缓存坐标，error说明使用了兜底机制
- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion;

/// 获取缓存的经纬度坐标（用于风险事件等立即需要坐标的场景）
- (CLLocationCoordinate2D)getCachedCoordinate;

@end

NS_ASSUME_NONNULL_END

#endif /* LocationManager_h */